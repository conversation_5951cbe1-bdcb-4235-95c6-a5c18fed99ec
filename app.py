#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة الذكي - التطبيق الرئيسي
Smart Surveillance System - Main Application

المطور: نظام المراقبة الذكي
التاريخ: 2025
الإصدار: 1.0.0
"""

import os
import json
import logging
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session, Response
from flask_socketio import SocketIO, emit, join_room, leave_room
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import cv2
import threading
import time

# إعداد التطبيق
app = Flask(__name__)

# تحميل الإعدادات
def load_config():
    """تحميل ملف الإعدادات"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ ملف الإعدادات غير موجود!")
        return {}
    except json.JSONDecodeError:
        print("❌ خطأ في قراءة ملف الإعدادات!")
        return {}

config = load_config()

# إعداد Flask
app.config['SECRET_KEY'] = config.get('server', {}).get('secret_key', 'default-secret-key')
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{config.get('database', {}).get('path', 'database.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد SocketIO للبث المباشر
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# نماذج قاعدة البيانات
class User(UserMixin, db.Model):
    """نموذج المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user, viewer
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Camera(db.Model):
    """نموذج الكاميرا"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    url = db.Column(db.String(500), nullable=False)
    type = db.Column(db.String(20), nullable=False)  # rtsp, usb, http
    username = db.Column(db.String(50))
    password = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    position_x = db.Column(db.Float, default=0)
    position_y = db.Column(db.Float, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Recording(db.Model):
    """نموذج التسجيل"""
    id = db.Column(db.Integer, primary_key=True)
    camera_id = db.Column(db.Integer, db.ForeignKey('camera.id'), nullable=False)
    filename = db.Column(db.String(200), nullable=False)
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime)
    file_size = db.Column(db.Integer)
    trigger_type = db.Column(db.String(20))  # manual, motion, schedule
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Event(db.Model):
    """نموذج الأحداث"""
    id = db.Column(db.Integer, primary_key=True)
    camera_id = db.Column(db.Integer, db.ForeignKey('camera.id'), nullable=False)
    event_type = db.Column(db.String(50), nullable=False)  # motion, face, object
    description = db.Column(db.Text)
    confidence = db.Column(db.Float)
    image_path = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# متغيرات عامة
camera_streams = {}
recording_threads = {}
motion_detectors = {}

# إنشاء مدير الكاميرات المحسن
from stream.enhanced_camera_manager import EnhancedCameraManager
from stream.camera_discovery import camera_discovery
enhanced_camera_manager = EnhancedCameraManager()

# الصفحات الأساسية
@app.route('/')
@login_required
def index():
    """الصفحة الرئيسية"""
    cameras = Camera.query.filter_by(is_active=True).all()
    recent_events = Event.query.order_by(Event.created_at.desc()).limit(10).all()
    return render_template('index.html', cameras=cameras, events=recent_events)

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            logger.info(f"تسجيل دخول ناجح للمستخدم: {username}")
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            logger.warning(f"محاولة دخول فاشلة للمستخدم: {username}")
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logger.info(f"تسجيل خروج للمستخدم: {current_user.username}")
    logout_user()
    return redirect(url_for('login'))

@app.route('/cameras')
@login_required
def cameras():
    """صفحة إدارة الكاميرات"""
    cameras = Camera.query.all()
    return render_template('cameras.html', cameras=cameras)

@app.route('/recordings')
@login_required
def recordings():
    """صفحة التسجيلات"""
    recordings = Recording.query.order_by(Recording.created_at.desc()).all()
    return render_template('recordings.html', recordings=recordings)

@app.route('/events')
@login_required
def events():
    """صفحة الأحداث"""
    events = Event.query.order_by(Event.created_at.desc()).all()
    return render_template('events.html', events=events)

@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات"""
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    return render_template('settings.html', config=config)

@app.route('/camera-discovery')
@login_required
def camera_discovery_page():
    """صفحة اكتشاف الكاميرات"""
    if current_user.role not in ['admin', 'user']:
        flash('غير مسموح لك بالوصول لهذه الصفحة', 'error')
        return redirect(url_for('index'))

    return render_template('camera_discovery.html')

# API endpoints للإعدادات
@app.route('/api/settings', methods=['GET', 'POST'])
@login_required
def api_settings():
    """API إدارة الإعدادات"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    if request.method == 'POST':
        try:
            new_settings = request.json

            # تحديث ملف الإعدادات
            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(new_settings, f, ensure_ascii=False, indent=2)

            # إعادة تحميل الإعدادات
            global config
            config = load_config()

            logger.info("تم تحديث الإعدادات بنجاح")
            return jsonify({'success': True, 'message': 'تم حفظ الإعدادات بنجاح'})

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            return jsonify({'success': False, 'message': str(e)}), 500

    # GET - إرجاع الإعدادات الحالية
    return jsonify({'success': True, 'settings': config})

@app.route('/api/settings/reset', methods=['POST'])
@login_required
def api_settings_reset():
    """استعادة الإعدادات الافتراضية"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        # الإعدادات الافتراضية
        default_settings = {
            "general": {
                "system_name": "نظام المراقبة الذكي",
                "language": "ar",
                "timezone": "Asia/Riyadh",
                "date_format": "DD/MM/YYYY",
                "auto_refresh": 30,
                "max_cameras": 16,
                "dark_mode": True,
                "enable_sounds": True
            },
            "server": {
                "host": "0.0.0.0",
                "port": 5000,
                "max_connections": 100,
                "session_timeout": 60,
                "enable_ssl": False,
                "debug": False,
                "log_level": "INFO",
                "secret_key": "default-secret-key"
            },
            "cameras": {
                "default_resolution": "1920x1080",
                "default_fps": 30,
                "stream_quality": "medium",
                "connection_timeout": 10,
                "max_retries": 3,
                "buffer_size": 1,
                "auto_reconnect": True,
                "enhance_image": True
            },
            "recording": {
                "path": "./recordings",
                "format": "mp4",
                "quality": "high",
                "segment_duration": 10,
                "max_storage_gb": 100,
                "retention_days": 30,
                "auto_recording": True,
                "pre_recording": False,
                "auto_cleanup": True
            },
            "detection": {
                "motion_sensitivity": 5,
                "motion_threshold": 15,
                "enable_motion": True,
                "face_confidence": 80,
                "face_model": "hog",
                "enable_face": True,
                "enable_face_recognition": False,
                "yolo_model": "yolov5s",
                "object_confidence": 70,
                "detect_person": True,
                "detect_car": True,
                "detect_animal": False,
                "enable_object": False
            },
            "database": {
                "path": "database.db"
            }
        }

        # حفظ الإعدادات الافتراضية
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(default_settings, f, ensure_ascii=False, indent=2)

        # إعادة تحميل الإعدادات
        global config
        config = load_config()

        logger.info("تم استعادة الإعدادات الافتراضية")
        return jsonify({'success': True, 'message': 'تم استعادة الإعدادات الافتراضية'})

    except Exception as e:
        logger.error(f"خطأ في استعادة الإعدادات الافتراضية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/settings/export')
@login_required
def api_settings_export():
    """تصدير الإعدادات"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        from flask import send_file
        import tempfile

        # إنشاء ملف مؤقت
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            temp_path = f.name

        return send_file(
            temp_path,
            as_attachment=True,
            download_name=f'surveillance_settings_{time.strftime("%Y%m%d_%H%M%S")}.json',
            mimetype='application/json'
        )

    except Exception as e:
        logger.error(f"خطأ في تصدير الإعدادات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/settings/import', methods=['POST'])
@login_required
def api_settings_import():
    """استيراد الإعدادات"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        if 'settings_file' not in request.files:
            return jsonify({'success': False, 'message': 'لم يتم رفع ملف'}), 400

        file = request.files['settings_file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'لم يتم اختيار ملف'}), 400

        # قراءة وتحليل الملف
        content = file.read().decode('utf-8')
        imported_settings = json.loads(content)

        # التحقق من صحة الإعدادات
        required_sections = ['general', 'server', 'cameras', 'recording', 'detection']
        for section in required_sections:
            if section not in imported_settings:
                return jsonify({'success': False, 'message': f'قسم {section} مفقود في الملف'}), 400

        # حفظ الإعدادات المستوردة
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(imported_settings, f, ensure_ascii=False, indent=2)

        # إعادة تحميل الإعدادات
        global config
        config = load_config()

        logger.info("تم استيراد الإعدادات بنجاح")
        return jsonify({'success': True, 'message': 'تم استيراد الإعدادات بنجاح'})

    except json.JSONDecodeError:
        return jsonify({'success': False, 'message': 'ملف JSON غير صالح'}), 400
    except Exception as e:
        logger.error(f"خطأ في استيراد الإعدادات: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# API endpoints للتخزين
@app.route('/api/storage/info')
@login_required
def api_storage_info():
    """معلومات التخزين"""
    try:
        import shutil

        # الحصول على معلومات التخزين
        total, used, free = shutil.disk_usage('.')

        # تحويل إلى GB
        total_gb = total / (1024**3)
        used_gb = used / (1024**3)
        free_gb = free / (1024**3)

        return jsonify({
            'success': True,
            'total': f'{total_gb:.1f} GB',
            'used': f'{used_gb:.1f} GB',
            'available': f'{free_gb:.1f} GB',
            'percentage': round((used_gb / total_gb) * 100, 1)
        })

    except Exception as e:
        logger.error(f"خطأ في جلب معلومات التخزين: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/storage/clean/<clean_type>', methods=['POST'])
@login_required
def api_storage_clean(clean_type):
    """تنظيف التخزين"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        deleted_count = 0

        if clean_type == 'recordings':
            # حذف التسجيلات القديمة
            import glob
            from datetime import datetime, timedelta

            retention_days = config.get('recording', {}).get('retention_days', 30)
            cutoff_date = datetime.now() - timedelta(days=retention_days)

            recordings_path = config.get('recording', {}).get('path', './recordings')
            for file_path in glob.glob(f"{recordings_path}/*.mp4"):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    os.remove(file_path)
                    deleted_count += 1

        elif clean_type == 'temp':
            # حذف الملفات المؤقتة
            import tempfile
            temp_dir = tempfile.gettempdir()
            for file_path in glob.glob(f"{temp_dir}/surveillance_*"):
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except:
                    pass

        elif clean_type == 'logs':
            # حذف السجلات القديمة
            for file_path in glob.glob("logs/*.log.*"):
                os.remove(file_path)
                deleted_count += 1

        elif clean_type == 'all':
            # تنظيف شامل
            # حذف التسجيلات القديمة
            retention_days = config.get('recording', {}).get('retention_days', 30)
            cutoff_date = datetime.now() - timedelta(days=retention_days)

            recordings_path = config.get('recording', {}).get('path', './recordings')
            for file_path in glob.glob(f"{recordings_path}/*.mp4"):
                file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                if file_time < cutoff_date:
                    os.remove(file_path)
                    deleted_count += 1

            # حذف الملفات المؤقتة
            import tempfile
            temp_dir = tempfile.gettempdir()
            for file_path in glob.glob(f"{temp_dir}/surveillance_*"):
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except:
                    pass

            # حذف السجلات القديمة
            for file_path in glob.glob("logs/*.log.*"):
                os.remove(file_path)
                deleted_count += 1

        logger.info(f"تم تنظيف {clean_type}: حذف {deleted_count} ملف")
        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'تم حذف {deleted_count} ملف'
        })

    except Exception as e:
        logger.error(f"خطأ في تنظيف التخزين: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# API endpoints للنسخ الاحتياطي
@app.route('/api/backup/create', methods=['POST'])
@login_required
def api_backup_create():
    """إنشاء نسخة احتياطية"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        import zipfile
        from datetime import datetime

        # إنشاء اسم الملف
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"backup_{timestamp}.zip"
        backup_path = f"./backups/{backup_filename}"

        # إنشاء مجلد النسخ الاحتياطي
        os.makedirs('./backups', exist_ok=True)

        # إنشاء النسخة الاحتياطية
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة قاعدة البيانات
            if os.path.exists('database.db'):
                zipf.write('database.db')

            # إضافة ملف الإعدادات
            if os.path.exists('config.json'):
                zipf.write('config.json')

            # إضافة الوجوه المعروفة
            if os.path.exists('known_faces'):
                for root, _, files in os.walk('known_faces'):
                    for file in files:
                        file_path = os.path.join(root, file)
                        zipf.write(file_path)

        logger.info(f"تم إنشاء نسخة احتياطية: {backup_filename}")
        return jsonify({
            'success': True,
            'filename': backup_filename,
            'message': 'تم إنشاء النسخة الاحتياطية بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/backup/list')
@login_required
def api_backup_list():
    """قائمة النسخ الاحتياطية"""
    try:
        import glob
        from datetime import datetime

        backups = []
        backup_files = glob.glob('./backups/*.zip')

        for backup_file in backup_files:
            stat = os.stat(backup_file)
            size_mb = stat.st_size / (1024 * 1024)

            backups.append({
                'filename': os.path.basename(backup_file),
                'date': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                'size': f'{size_mb:.1f} MB',
                'type': 'auto' if 'auto' in backup_file else 'manual'
            })

        # ترتيب حسب التاريخ (الأحدث أولاً)
        backups.sort(key=lambda x: x['date'], reverse=True)

        return jsonify({
            'success': True,
            'backups': backups
        })

    except Exception as e:
        logger.error(f"خطأ في جلب قائمة النسخ الاحتياطية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/backup/restore/<filename>', methods=['POST'])
@login_required
def api_backup_restore_specific(filename):
    """استعادة نسخة احتياطية محددة"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        import zipfile

        backup_path = f"./backups/{filename}"
        if not os.path.exists(backup_path):
            return jsonify({'success': False, 'message': 'النسخة الاحتياطية غير موجودة'}), 404

        # استعادة النسخة الاحتياطية
        with zipfile.ZipFile(backup_path, 'r') as zipf:
            zipf.extractall('.')

        # إعادة تحميل الإعدادات
        global config
        config = load_config()

        logger.info(f"تم استعادة النسخة الاحتياطية: {filename}")
        return jsonify({
            'success': True,
            'message': 'تم استعادة النسخة الاحتياطية بنجاح'
        })

    except Exception as e:
        logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/backup/delete/<filename>', methods=['DELETE'])
@login_required
def api_backup_delete(filename):
    """حذف نسخة احتياطية"""
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': 'غير مصرح'}), 403

    try:
        backup_path = f"./backups/{filename}"
        if not os.path.exists(backup_path):
            return jsonify({'success': False, 'message': 'النسخة الاحتياطية غير موجودة'}), 404

        os.remove(backup_path)

        logger.info(f"تم حذف النسخة الاحتياطية: {filename}")
        return jsonify({
            'success': True,
            'message': 'تم حذف النسخة الاحتياطية'
        })

    except Exception as e:
        logger.error(f"خطأ في حذف النسخة الاحتياطية: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

# API endpoints
@app.route('/api/cameras', methods=['GET', 'POST'])
@login_required
def api_cameras():
    """API إدارة الكاميرات"""
    if request.method == 'POST':
        data = request.json
        camera = Camera(
            name=data['name'],
            url=data['url'],
            type=data['type'],
            username=data.get('username'),
            password=data.get('password')
        )
        db.session.add(camera)
        db.session.commit()
        return jsonify({'success': True, 'message': 'تم إضافة الكاميرا بنجاح'})

    cameras = Camera.query.all()
    return jsonify([{
        'id': c.id,
        'name': c.name,
        'url': c.url,
        'type': c.type,
        'is_active': c.is_active
    } for c in cameras])

@app.route('/api/cameras/auto-discover', methods=['POST'])
@login_required
def api_auto_discover():
    """API الاكتشاف التلقائي للكاميرات"""
    try:
        from utils.camera_discovery import CameraDiscovery

        data = request.json
        network_range = data.get('network_range', '192.168.1.1-254')
        protocols = data.get('protocols', {'rtsp': True, 'http': True, 'onvif': True})

        discovery = CameraDiscovery()
        cameras = discovery.scan_network_range(network_range, protocols)

        return jsonify({
            'success': True,
            'cameras': cameras,
            'message': f'تم اكتشاف {len(cameras)} كاميرا'
        })

    except Exception as e:
        logger.error(f"خطأ في الاكتشاف التلقائي: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في الاكتشاف: {str(e)}'
        })

@app.route('/api/cameras/detect-channels', methods=['POST'])
@login_required
def api_detect_channels():
    """API اكتشاف قنوات الكاميرا"""
    try:
        from utils.camera_discovery import CameraDiscovery

        data = request.json
        url = data.get('url')
        username = data.get('username', '')
        password = data.get('password', '')

        if not url:
            return jsonify({'success': False, 'message': 'عنوان الكاميرا مطلوب'})

        discovery = CameraDiscovery()
        channels = discovery.detect_channels(url, username, password)

        return jsonify({
            'success': True,
            'channels': channels,
            'message': f'تم اكتشاف {len(channels)} قناة'
        })

    except Exception as e:
        logger.error(f"خطأ في اكتشاف القنوات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في اكتشاف القنوات: {str(e)}'
        })

@app.route('/api/cameras/scan-network', methods=['POST'])
@login_required
def api_scan_network():
    """API فحص الشبكة المحلية"""
    try:
        from utils.camera_discovery import CameraDiscovery

        discovery = CameraDiscovery()
        devices = discovery.scan_local_network()

        return jsonify({
            'success': True,
            'devices': devices,
            'message': f'تم العثور على {len(devices)} جهاز'
        })

    except Exception as e:
        logger.error(f"خطأ في فحص الشبكة: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في فحص الشبكة: {str(e)}'
        })

@app.route('/api/cameras/test', methods=['POST'])
@login_required
def api_test_camera():
    """API اختبار اتصال الكاميرا"""
    try:
        data = request.json
        camera_type = data.get('type')
        url = data.get('url')
        username = data.get('username', '')
        password = data.get('password', '')

        if not camera_type or not url:
            return jsonify({'success': False, 'message': 'نوع الكاميرا والعنوان مطلوبان'})

        # اختبار الاتصال حسب النوع
        if camera_type == 'rtsp':
            success = test_rtsp_connection(url, username, password)
        elif camera_type == 'http':
            success = test_http_connection(url, username, password)
        elif camera_type == 'usb':
            success = test_usb_connection(url)
        else:
            success = False

        if success:
            return jsonify({'success': True, 'message': 'تم الاتصال بنجاح'})
        else:
            return jsonify({'success': False, 'message': 'فشل في الاتصال'})

    except Exception as e:
        logger.error(f"خطأ في اختبار الكاميرا: {str(e)}")
        return jsonify({'success': False, 'message': f'خطأ في الاختبار: {str(e)}'})

def test_rtsp_connection(url: str, username: str = '', password: str = '') -> bool:
    """اختبار اتصال RTSP"""
    try:
        if username and password:
            # إدراج بيانات الاعتماد في URL
            from urllib.parse import urlparse
            parsed = urlparse(url)
            url = f"{parsed.scheme}://{username}:{password}@{parsed.netloc}{parsed.path}"

        cap = cv2.VideoCapture(url)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            return ret and frame is not None
        return False
    except Exception:
        return False

def test_http_connection(url: str, username: str = '', password: str = '') -> bool:
    """اختبار اتصال HTTP"""
    try:
        import requests
        auth = None
        if username and password:
            auth = (username, password)

        response = requests.get(url, auth=auth, timeout=5)
        return response.status_code == 200
    except Exception:
        return False

def test_usb_connection(device_id: str) -> bool:
    """اختبار اتصال USB"""
    try:
        device_index = int(device_id) if device_id.isdigit() else 0
        cap = cv2.VideoCapture(device_index)
        if cap.isOpened():
            ret, frame = cap.read()
            cap.release()
            return ret and frame is not None
        return False
    except Exception:
        return False

# إنشاء قاعدة البيانات والمستخدم الافتراضي
def init_database():
    """إنشاء قاعدة البيانات والبيانات الافتراضية"""
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            logger.info("تم إنشاء المستخدم الافتراضي: admin/admin123")

if __name__ == '__main__':
    # إنشاء المجلدات المطلوبة
    os.makedirs('logs', exist_ok=True)
    os.makedirs('recordings', exist_ok=True)
    os.makedirs('known_faces', exist_ok=True)
    
# دوال مساعدة للبث
def create_placeholder_frame(camera_id: int, message: str) -> bytes:
    """إنشاء إطار مؤقت مع رسالة"""
    import cv2
    import numpy as np

    # إنشاء إطار أسود
    frame = np.zeros((480, 640, 3), dtype=np.uint8)

    # إضافة النص
    font = cv2.FONT_HERSHEY_SIMPLEX
    text_lines = [
        f"Camera {camera_id}",
        message,
        time.strftime("%Y-%m-%d %H:%M:%S")
    ]

    y_offset = 200
    for line in text_lines:
        text_size = cv2.getTextSize(line, font, 1, 2)[0]
        x = (frame.shape[1] - text_size[0]) // 2
        cv2.putText(frame, line, (x, y_offset), font, 1, (255, 255, 255), 2)
        y_offset += 50

    # تحويل إلى JPEG
    _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 70])
    return buffer.tobytes()

# Routes للبث المباشر
@app.route('/api/cameras/<int:camera_id>/stream')
def camera_stream(camera_id):
    """بث مباشر للكاميرا"""
    def generate():
        while True:
            try:
                # الحصول على إطار من مدير الكاميرات المحسن
                frame_data = enhanced_camera_manager.get_camera_frame(camera_id)

                if frame_data:
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' +
                           frame_data + b'\r\n')
                else:
                    # إرسال إطار فارغ أو رسالة خطأ
                    placeholder_frame = create_placeholder_frame(camera_id, "جاري الاتصال...")
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' +
                           placeholder_frame + b'\r\n')

                time.sleep(0.033)  # ~30 FPS

            except Exception as e:
                logger.error(f"خطأ في بث الكاميرا {camera_id}: {e}")
                placeholder_frame = create_placeholder_frame(camera_id, "خطأ في الاتصال")
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' +
                       placeholder_frame + b'\r\n')
                time.sleep(1)

    return Response(generate(), mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/api/cameras/<int:camera_id>/start', methods=['POST'])
@login_required
def start_camera_stream(camera_id):
    """بدء بث كاميرا"""
    try:
        success = enhanced_camera_manager.start_camera(camera_id)

        if success:
            return jsonify({
                'success': True,
                'message': f'تم بدء بث الكاميرا {camera_id}'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'فشل في بدء بث الكاميرا {camera_id}'
            }), 400

    except Exception as e:
        logger.error(f"خطأ في بدء بث الكاميرا {camera_id}: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/cameras/status')
@login_required
def cameras_status():
    """الحصول على حالة جميع الكاميرات"""
    try:
        status = enhanced_camera_manager.get_all_cameras_status()

        # إضافة معلومات إضافية لكل كاميرا
        enhanced_status = {}
        for camera_id, camera_status in status.items():
            enhanced_status[camera_id] = {
                'is_active': camera_status.get('is_active', False),
                'is_connected': camera_status.get('is_connected', False),
                'fps': camera_status.get('fps', 0),
                'resolution': camera_status.get('resolution', '1920x1080'),
                'last_frame_time': camera_status.get('last_frame_time', ''),
                'error_count': camera_status.get('error_count', 0),
                'uptime': camera_status.get('uptime', '00:00:00'),
                'bitrate': camera_status.get('bitrate', '0 kbps')
            }

        return jsonify({
            'success': True,
            'cameras': enhanced_status
        })
    except Exception as e:
        logger.error(f"خطأ في جلب حالة الكاميرات: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/cameras/discover', methods=['POST'])
@login_required
def discover_cameras():
    """اكتشاف الكاميرات في الشبكة"""
    try:
        data = request.get_json() or {}
        network_range = data.get('network_range')
        max_workers = data.get('max_workers', 50)

        # بدء الاكتشاف في خيط منفصل
        import threading
        discovery_thread = threading.Thread(
            target=camera_discovery.discover_cameras,
            args=(network_range, max_workers),
            daemon=True
        )
        discovery_thread.start()

        return jsonify({
            'success': True,
            'message': 'تم بدء عملية اكتشاف الكاميرات'
        })

    except Exception as e:
        logger.error(f"خطأ في بدء اكتشاف الكاميرات: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/cameras/discover/status')
@login_required
def discovery_status():
    """الحصول على حالة اكتشاف الكاميرات"""
    try:
        status = camera_discovery.get_scan_status()
        return jsonify({
            'success': True,
            **status
        })
    except Exception as e:
        logger.error(f"خطأ في جلب حالة الاكتشاف: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/cameras/test', methods=['POST'])
@login_required
def test_camera():
    """اختبار الاتصال بكاميرا"""
    try:
        data = request.get_json()
        camera_info = data.get('camera_info')

        if not camera_info:
            return jsonify({
                'success': False,
                'message': 'معلومات الكاميرا مطلوبة'
            }), 400

        # اختبار الاتصال
        is_working = camera_discovery.test_camera_connection(camera_info)

        return jsonify({
            'success': True,
            'is_working': is_working,
            'message': 'الكاميرا تعمل بشكل صحيح' if is_working else 'فشل في الاتصال بالكاميرا'
        })

    except Exception as e:
        logger.error(f"خطأ في اختبار الكاميرا: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@app.route('/api/cameras/add', methods=['POST'])
@login_required
def add_discovered_camera():
    """إضافة كاميرا مكتشفة إلى النظام"""
    try:
        data = request.get_json()
        camera_info = data.get('camera_info')
        camera_name = data.get('name', f"Camera {camera_info.get('ip', 'Unknown')}")

        if not camera_info:
            return jsonify({
                'success': False,
                'message': 'معلومات الكاميرا مطلوبة'
            }), 400

        # إنشاء كاميرا جديدة في قاعدة البيانات
        new_camera = Camera(
            name=camera_name,
            rtsp_url=camera_info.get('url', ''),
            username=data.get('username', ''),
            password=data.get('password', ''),
            is_active=True
        )

        db.session.add(new_camera)
        db.session.commit()

        # إضافة الكاميرا إلى مدير الكاميرات
        success = enhanced_camera_manager.add_camera(
            new_camera.id,
            camera_info.get('url', ''),
            data.get('username'),
            data.get('password')
        )

        if success:
            return jsonify({
                'success': True,
                'camera_id': new_camera.id,
                'message': f'تم إضافة الكاميرا {camera_name} بنجاح'
            })
        else:
            # حذف من قاعدة البيانات إذا فشلت الإضافة
            db.session.delete(new_camera)
            db.session.commit()

            return jsonify({
                'success': False,
                'message': 'فشل في إضافة الكاميرا إلى النظام'
            }), 500

    except Exception as e:
        logger.error(f"خطأ في إضافة الكاميرا: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

# SocketIO events للبث المباشر
@socketio.on('connect', namespace='/camera')
def handle_camera_connect():
    """اتصال عميل جديد"""
    logger.info(f"عميل جديد متصل للبث المباشر")
    emit('connected', {'message': 'متصل بخدمة البث المباشر'})

@socketio.on('start_camera', namespace='/camera')
def handle_start_camera(data):
    """بدء بث كاميرا عبر SocketIO"""
    camera_id = data.get('camera_id')
    if camera_id:
        success = enhanced_camera_manager.start_camera(camera_id)
        emit('camera_status', {
            'camera_id': camera_id,
            'status': 'started' if success else 'failed'
        })

if __name__ == '__main__':
    # إنشاء المجلدات المطلوبة
    os.makedirs('static/uploads', exist_ok=True)
    os.makedirs('static/events', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    os.makedirs('recordings', exist_ok=True)
    os.makedirs('known_faces', exist_ok=True)

    # إنشاء قاعدة البيانات
    init_database()

    # إضافة كاميرات تجريبية للاختبار
    try:
        # كاميرا تجريبية 1
        enhanced_camera_manager.add_camera(1, "rtsp://admin:admin@192.168.1.100:554/stream1", "admin", "admin")
        # كاميرا تجريبية 2
        enhanced_camera_manager.add_camera(2, "rtsp://admin:admin@192.168.1.101:554/stream1", "admin", "admin")
        # كاميرا تجريبية 3
        enhanced_camera_manager.add_camera(3, "rtsp://admin:admin@*************:554/stream1", "admin", "admin")
        # كاميرا تجريبية 4
        enhanced_camera_manager.add_camera(4, "rtsp://admin:admin@*************:554/stream1", "admin", "admin")

        logger.info("تم إضافة كاميرات تجريبية")
    except Exception as e:
        logger.error(f"خطأ في إضافة الكاميرات التجريبية: {e}")

    # تشغيل التطبيق
    host = config.get('server', {}).get('host', '0.0.0.0')
    port = config.get('server', {}).get('port', 5000)
    debug = config.get('system', {}).get('debug', True)

    logger.info(f"🚀 بدء تشغيل نظام المراقبة الذكي على {host}:{port}")
    socketio.run(app, host=host, port=port, debug=debug, allow_unsafe_werkzeug=True)
