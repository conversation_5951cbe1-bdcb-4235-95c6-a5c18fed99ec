#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
نظام اكتشاف الكاميرات التلقائي
يقوم بفحص الشبكة المحلية للعثور على كاميرات IP
"""

import socket
import threading
import time
import requests
import cv2
from concurrent.futures import ThreadPoolExecutor, as_completed
import ipaddress
import logging
from typing import List, Dict, Optional
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)

class CameraDiscovery:
    """مدير اكتشاف الكاميرات"""
    
    def __init__(self):
        self.discovered_cameras = []
        self.scan_progress = 0
        self.is_scanning = False
        
        # منافذ شائعة للكاميرات
        self.common_ports = [80, 554, 8080, 8081, 8554, 1935, 5000]
        
        # مسارات RTSP شائعة
        self.rtsp_paths = [
            '/stream',
            '/live',
            '/cam/realmonitor',
            '/h264',
            '/video',
            '/mjpeg',
            '/axis-media/media.amp',
            '/onvif/device_service',
            '/cgi-bin/mjpg/video.cgi',
            '/videostream.cgi',
            '/video.mjpg',
            '/cam1/mpeg4',
            '/cam1/h264',
            '/streaming/channels/1',
            '/streaming/channels/101'
        ]
        
        # User agents للكاميرات المختلفة
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'VLC media player',
            'ONVIF/2.0',
            'Camera Client'
        ]
    
    def get_local_network_range(self) -> str:
        """الحصول على نطاق الشبكة المحلية"""
        try:
            # الحصول على IP المحلي
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            
            # تحديد نطاق الشبكة
            network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)
            return str(network)
            
        except Exception as e:
            logger.error(f"خطأ في تحديد نطاق الشبكة: {e}")
            return "***********/24"  # افتراضي
    
    def scan_port(self, ip: str, port: int, timeout: float = 2.0) -> bool:
        """فحص منفذ محدد"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def check_http_camera(self, ip: str, port: int) -> Optional[Dict]:
        """فحص كاميرا HTTP"""
        try:
            urls_to_try = [
                f"http://{ip}:{port}",
                f"http://{ip}:{port}/video",
                f"http://{ip}:{port}/mjpeg",
                f"http://{ip}:{port}/cgi-bin/mjpg/video.cgi"
            ]
            
            for url in urls_to_try:
                try:
                    response = requests.get(url, timeout=3, stream=True)
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '').lower()
                        
                        if 'image' in content_type or 'video' in content_type:
                            return {
                                'ip': ip,
                                'port': port,
                                'type': 'HTTP',
                                'url': url,
                                'content_type': content_type,
                                'status': 'active'
                            }
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"خطأ في فحص HTTP للعنوان {ip}:{port} - {e}")
        
        return None
    
    def check_rtsp_camera(self, ip: str, port: int = 554) -> Optional[Dict]:
        """فحص كاميرا RTSP"""
        try:
            for path in self.rtsp_paths:
                rtsp_url = f"rtsp://{ip}:{port}{path}"
                
                try:
                    # محاولة الاتصال بـ OpenCV
                    cap = cv2.VideoCapture(rtsp_url)
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                    
                    # محاولة قراءة إطار
                    ret, frame = cap.read()
                    cap.release()
                    
                    if ret and frame is not None:
                        return {
                            'ip': ip,
                            'port': port,
                            'type': 'RTSP',
                            'url': rtsp_url,
                            'path': path,
                            'status': 'active'
                        }
                        
                except Exception as e:
                    logger.debug(f"فشل RTSP {rtsp_url}: {e}")
                    continue
                    
        except Exception as e:
            logger.debug(f"خطأ في فحص RTSP للعنوان {ip}:{port} - {e}")
        
        return None
    
    def check_onvif_camera(self, ip: str, port: int = 80) -> Optional[Dict]:
        """فحص كاميرا ONVIF"""
        try:
            onvif_url = f"http://{ip}:{port}/onvif/device_service"
            
            # SOAP request للحصول على معلومات الجهاز
            soap_body = """<?xml version="1.0" encoding="UTF-8"?>
            <soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
                <soap:Body>
                    <tds:GetDeviceInformation xmlns:tds="http://www.onvif.org/ver10/device/wsdl"/>
                </soap:Body>
            </soap:Envelope>"""
            
            headers = {
                'Content-Type': 'application/soap+xml',
                'SOAPAction': 'http://www.onvif.org/ver10/device/wsdl/GetDeviceInformation'
            }
            
            response = requests.post(onvif_url, data=soap_body, headers=headers, timeout=5)
            
            if response.status_code == 200 and 'soap' in response.text.lower():
                return {
                    'ip': ip,
                    'port': port,
                    'type': 'ONVIF',
                    'url': onvif_url,
                    'status': 'active'
                }
                
        except Exception as e:
            logger.debug(f"خطأ في فحص ONVIF للعنوان {ip}:{port} - {e}")
        
        return None
    
    def scan_single_ip(self, ip: str) -> List[Dict]:
        """فحص عنوان IP واحد"""
        cameras_found = []
        
        try:
            # فحص المنافذ الشائعة
            for port in self.common_ports:
                if self.scan_port(ip, port, timeout=1.0):
                    
                    # فحص HTTP
                    if port in [80, 8080, 8081]:
                        camera = self.check_http_camera(ip, port)
                        if camera:
                            cameras_found.append(camera)
                    
                    # فحص RTSP
                    if port in [554, 8554]:
                        camera = self.check_rtsp_camera(ip, port)
                        if camera:
                            cameras_found.append(camera)
                    
                    # فحص ONVIF
                    if port in [80, 8080]:
                        camera = self.check_onvif_camera(ip, port)
                        if camera:
                            cameras_found.append(camera)
            
            # إضافة معلومات إضافية
            for camera in cameras_found:
                camera['hostname'] = self.get_hostname(ip)
                camera['mac_address'] = self.get_mac_address(ip)
                camera['discovered_at'] = time.time()
                
        except Exception as e:
            logger.error(f"خطأ في فحص العنوان {ip}: {e}")
        
        return cameras_found
    
    def get_hostname(self, ip: str) -> Optional[str]:
        """الحصول على اسم المضيف"""
        try:
            hostname = socket.gethostbyaddr(ip)[0]
            return hostname
        except:
            return None
    
    def get_mac_address(self, ip: str) -> Optional[str]:
        """الحصول على عنوان MAC (محدود)"""
        try:
            # هذا يعمل فقط في نفس الشبكة المحلية
            import subprocess
            import re
            
            # استخدام ARP للحصول على MAC
            result = subprocess.run(['arp', '-n', ip], capture_output=True, text=True)
            if result.returncode == 0:
                mac_match = re.search(r'([0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}', result.stdout)
                if mac_match:
                    return mac_match.group(0)
        except:
            pass
        
        return None
    
    def discover_cameras(self, network_range: str = None, max_workers: int = 50) -> List[Dict]:
        """اكتشاف الكاميرات في الشبكة"""
        self.is_scanning = True
        self.scan_progress = 0
        self.discovered_cameras = []
        
        try:
            if not network_range:
                network_range = self.get_local_network_range()
            
            logger.info(f"بدء فحص الشبكة: {network_range}")
            
            # الحصول على قائمة عناوين IP
            network = ipaddress.IPv4Network(network_range, strict=False)
            ip_list = [str(ip) for ip in network.hosts()]
            
            total_ips = len(ip_list)
            completed = 0
            
            # فحص متوازي
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_ip = {executor.submit(self.scan_single_ip, ip): ip for ip in ip_list}
                
                for future in as_completed(future_to_ip):
                    ip = future_to_ip[future]
                    completed += 1
                    self.scan_progress = int((completed / total_ips) * 100)
                    
                    try:
                        cameras = future.result()
                        if cameras:
                            self.discovered_cameras.extend(cameras)
                            logger.info(f"تم العثور على {len(cameras)} كاميرا في {ip}")
                    except Exception as e:
                        logger.error(f"خطأ في فحص {ip}: {e}")
            
            logger.info(f"انتهى الفحص. تم العثور على {len(self.discovered_cameras)} كاميرا")
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف الكاميرات: {e}")
        finally:
            self.is_scanning = False
            self.scan_progress = 100
        
        return self.discovered_cameras
    
    def get_scan_status(self) -> Dict:
        """الحصول على حالة الفحص"""
        return {
            'is_scanning': self.is_scanning,
            'progress': self.scan_progress,
            'cameras_found': len(self.discovered_cameras),
            'cameras': self.discovered_cameras
        }
    
    def test_camera_connection(self, camera_info: Dict) -> bool:
        """اختبار الاتصال بكاميرا"""
        try:
            if camera_info['type'] == 'RTSP':
                cap = cv2.VideoCapture(camera_info['url'])
                ret, frame = cap.read()
                cap.release()
                return ret and frame is not None
                
            elif camera_info['type'] == 'HTTP':
                response = requests.get(camera_info['url'], timeout=5)
                return response.status_code == 200
                
            elif camera_info['type'] == 'ONVIF':
                response = requests.get(camera_info['url'], timeout=5)
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"خطأ في اختبار الكاميرا: {e}")
        
        return False

# إنشاء مثيل عام
camera_discovery = CameraDiscovery()
