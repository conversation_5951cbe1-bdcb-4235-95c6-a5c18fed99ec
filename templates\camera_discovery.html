{% extends "base.html" %}

{% block title %}اكتشاف الكاميرات{% endblock %}

{% block extra_css %}
<style>
.discovery-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    color: white;
    border: none;
}

.camera-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.camera-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.8rem;
    padding: 4px 8px;
}

.progress-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.scan-controls {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.camera-type-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.type-rtsp { background: #28a745; }
.type-http { background: #007bff; }
.type-onvif { background: #ffc107; color: #000; }

.network-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 8px;
}

.network-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.network-input:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

.btn-scan {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    border-radius: 25px;
    padding: 10px 30px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.btn-scan:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-test {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.8rem;
}

.btn-add {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    border-radius: 20px;
    padding: 5px 15px;
    font-size: 0.8rem;
}

.scanning-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: rgba(255, 255, 255, 0.7);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="discovery-card p-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-search me-2"></i>
                            اكتشاف الكاميرات التلقائي
                        </h2>
                        <p class="mb-0 opacity-75">
                            فحص الشبكة المحلية للعثور على كاميرات IP المتاحة
                        </p>
                    </div>
                    <div class="text-end">
                        <div class="h4 mb-0" id="camerasFoundCount">0</div>
                        <small>كاميرا مكتشفة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="discovery-card scan-controls">
                <div class="row align-items-end">
                    <div class="col-md-4">
                        <label class="form-label">نطاق الشبكة</label>
                        <input type="text" class="form-control network-input" 
                               id="networkRange" 
                               placeholder="***********/24"
                               value="***********/24">
                        <small class="text-muted">اتركه فارغاً للكشف التلقائي</small>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">عدد الخيوط</label>
                        <select class="form-control network-input" id="maxWorkers">
                            <option value="25">25 (بطيء)</option>
                            <option value="50" selected>50 (متوسط)</option>
                            <option value="100">100 (سريع)</option>
                            <option value="200">200 (سريع جداً)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-scan w-100" id="startScanBtn" onclick="startDiscovery()">
                            <i class="fas fa-search me-2"></i>
                            بدء الفحص
                        </button>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-light w-100" onclick="refreshResults()">
                            <i class="fas fa-sync-alt me-2"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط التقدم -->
    <div class="row mb-4" id="progressContainer" style="display: none;">
        <div class="col-12">
            <div class="discovery-card progress-container">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>جاري فحص الشبكة...</span>
                    <span id="progressText">0%</span>
                </div>
                <div class="progress" style="height: 10px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-success" 
                         id="progressBar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="mt-2 d-flex justify-content-between">
                    <small id="scanStatus">بدء الفحص...</small>
                    <small id="camerasFound">0 كاميرا مكتشفة</small>
                </div>
            </div>
        </div>
    </div>

    <!-- نتائج الاكتشاف -->
    <div class="row">
        <div class="col-12">
            <div class="discovery-card p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="mb-0">
                        <i class="fas fa-video me-2"></i>
                        الكاميرات المكتشفة
                    </h4>
                    <div>
                        <button class="btn btn-outline-light btn-sm me-2" onclick="testAllCameras()">
                            <i class="fas fa-check-circle me-1"></i>
                            اختبار الكل
                        </button>
                        <button class="btn btn-success btn-sm" onclick="addAllCameras()">
                            <i class="fas fa-plus me-1"></i>
                            إضافة الكل
                        </button>
                    </div>
                </div>

                <!-- قائمة الكاميرات -->
                <div id="camerasList">
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h5>لم يتم العثور على كاميرات بعد</h5>
                        <p>ابدأ عملية الفحص للعثور على الكاميرات في شبكتك</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة كاميرا -->
<div class="modal fade" id="addCameraModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة كاميرا جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm">
                    <div class="mb-3">
                        <label class="form-label">اسم الكاميرا</label>
                        <input type="text" class="form-control" id="cameraName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رابط البث</label>
                        <input type="text" class="form-control" id="cameraUrl" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="cameraUsername">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="cameraPassword">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmAddCamera()">إضافة الكاميرا</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let discoveredCameras = [];
let scanInterval = null;
let currentCameraInfo = null;

// بدء عملية الاكتشاف
function startDiscovery() {
    const networkRange = document.getElementById('networkRange').value.trim();
    const maxWorkers = parseInt(document.getElementById('maxWorkers').value);
    const startBtn = document.getElementById('startScanBtn');
    
    // تعطيل الزر وإظهار التحميل
    startBtn.disabled = true;
    startBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الفحص...';
    
    // إظهار شريط التقدم
    document.getElementById('progressContainer').style.display = 'block';
    
    // بدء الفحص
    fetch('/api/cameras/discover', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            network_range: networkRange || null,
            max_workers: maxWorkers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // بدء مراقبة التقدم
            scanInterval = setInterval(checkScanProgress, 1000);
        } else {
            showError('فشل في بدء عملية الفحص: ' + data.message);
            resetScanUI();
        }
    })
    .catch(error => {
        showError('خطأ في الاتصال: ' + error.message);
        resetScanUI();
    });
}

// مراقبة تقدم الفحص
function checkScanProgress() {
    fetch('/api/cameras/discover/status')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateProgressUI(data);
            
            if (!data.is_scanning) {
                // انتهى الفحص
                clearInterval(scanInterval);
                resetScanUI();
                displayCameras(data.cameras);
            }
        }
    })
    .catch(error => {
        console.error('خطأ في مراقبة التقدم:', error);
    });
}

// تحديث واجهة التقدم
function updateProgressUI(data) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const scanStatus = document.getElementById('scanStatus');
    const camerasFound = document.getElementById('camerasFound');
    
    progressBar.style.width = data.progress + '%';
    progressText.textContent = data.progress + '%';
    
    if (data.is_scanning) {
        scanStatus.textContent = 'جاري فحص الشبكة...';
    } else {
        scanStatus.textContent = 'انتهى الفحص';
    }
    
    camerasFound.textContent = data.cameras_found + ' كاميرا مكتشفة';
    document.getElementById('camerasFoundCount').textContent = data.cameras_found;
}

// إعادة تعيين واجهة الفحص
function resetScanUI() {
    const startBtn = document.getElementById('startScanBtn');
    startBtn.disabled = false;
    startBtn.innerHTML = '<i class="fas fa-search me-2"></i>بدء الفحص';
    
    setTimeout(() => {
        document.getElementById('progressContainer').style.display = 'none';
    }, 2000);
}

// عرض الكاميرات المكتشفة
function displayCameras(cameras) {
    discoveredCameras = cameras;
    const camerasList = document.getElementById('camerasList');
    
    if (cameras.length === 0) {
        camerasList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>لم يتم العثور على كاميرات</h5>
                <p>تأكد من أن الكاميرات متصلة بالشبكة وجرب نطاق شبكة مختلف</p>
            </div>
        `;
        return;
    }
    
    let html = '';
    cameras.forEach((camera, index) => {
        const typeClass = `type-${camera.type.toLowerCase()}`;
        const typeIcon = camera.type === 'RTSP' ? 'video' : camera.type === 'HTTP' ? 'globe' : 'cog';
        
        html += `
            <div class="camera-item" data-index="${index}">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <div class="camera-type-icon ${typeClass}">
                            <i class="fas fa-${typeIcon}"></i>
                        </div>
                    </div>
                    <div class="col">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${camera.ip}:${camera.port}</h6>
                                <p class="mb-1 small opacity-75">${camera.url}</p>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-primary status-badge">${camera.type}</span>
                                    <span class="badge bg-success status-badge" id="status-${index}">مكتشفة</span>
                                    ${camera.hostname ? `<span class="badge bg-info status-badge">${camera.hostname}</span>` : ''}
                                </div>
                            </div>
                            <div class="text-end">
                                <button class="btn btn-test btn-sm me-1" onclick="testCamera(${index})">
                                    <i class="fas fa-check me-1"></i>اختبار
                                </button>
                                <button class="btn btn-add btn-sm" onclick="showAddCameraModal(${index})">
                                    <i class="fas fa-plus me-1"></i>إضافة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    camerasList.innerHTML = html;
}

// اختبار كاميرا
function testCamera(index) {
    const camera = discoveredCameras[index];
    const statusElement = document.getElementById(`status-${index}`);
    
    statusElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاختبار...';
    statusElement.className = 'badge bg-warning status-badge';
    
    fetch('/api/cameras/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            camera_info: camera
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.is_working) {
            statusElement.innerHTML = '<i class="fas fa-check"></i> تعمل';
            statusElement.className = 'badge bg-success status-badge';
        } else {
            statusElement.innerHTML = '<i class="fas fa-times"></i> لا تعمل';
            statusElement.className = 'badge bg-danger status-badge';
        }
    })
    .catch(error => {
        statusElement.innerHTML = '<i class="fas fa-exclamation"></i> خطأ';
        statusElement.className = 'badge bg-danger status-badge';
    });
}

// إظهار نافذة إضافة كاميرا
function showAddCameraModal(index) {
    currentCameraInfo = discoveredCameras[index];
    
    document.getElementById('cameraName').value = `Camera ${currentCameraInfo.ip}`;
    document.getElementById('cameraUrl').value = currentCameraInfo.url;
    document.getElementById('cameraUsername').value = '';
    document.getElementById('cameraPassword').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('addCameraModal'));
    modal.show();
}

// تأكيد إضافة الكاميرا
function confirmAddCamera() {
    const name = document.getElementById('cameraName').value;
    const username = document.getElementById('cameraUsername').value;
    const password = document.getElementById('cameraPassword').value;
    
    if (!name.trim()) {
        showError('اسم الكاميرا مطلوب');
        return;
    }
    
    fetch('/api/cameras/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            camera_info: currentCameraInfo,
            name: name,
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('تم إضافة الكاميرا بنجاح');
            bootstrap.Modal.getInstance(document.getElementById('addCameraModal')).hide();
        } else {
            showError('فشل في إضافة الكاميرا: ' + data.message);
        }
    })
    .catch(error => {
        showError('خطأ في الاتصال: ' + error.message);
    });
}

// تحديث النتائج
function refreshResults() {
    fetch('/api/cameras/discover/status')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayCameras(data.cameras);
            document.getElementById('camerasFoundCount').textContent = data.cameras_found;
        }
    })
    .catch(error => {
        showError('خطأ في تحديث النتائج: ' + error.message);
    });
}

// اختبار جميع الكاميرات
function testAllCameras() {
    discoveredCameras.forEach((camera, index) => {
        setTimeout(() => testCamera(index), index * 500);
    });
}

// إضافة جميع الكاميرات
function addAllCameras() {
    if (confirm('هل تريد إضافة جميع الكاميرات المكتشفة؟')) {
        discoveredCameras.forEach((camera, index) => {
            setTimeout(() => {
                currentCameraInfo = camera;
                confirmAddCamera();
            }, index * 1000);
        });
    }
}

// عرض رسائل النجاح والخطأ
function showSuccess(message) {
    // يمكن تحسينها لاحقاً
    alert(message);
}

function showError(message) {
    // يمكن تحسينها لاحقاً
    alert(message);
}

// تحديث النتائج عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    refreshResults();
});
</script>
{% endblock %}
