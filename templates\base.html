<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام المراقبة الذكي{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            direction: rtl;
        }
        
        .navbar {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-bottom: 2px solid #3498db;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #3498db !important;
        }
        
        .nav-link {
            color: #ecf0f1 !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: #3498db !important;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #2c3e50, #34495e);
            min-height: calc(100vh - 76px);
            border-left: 2px solid #3498db;
        }
        
        .sidebar .nav-link {
            color: #bdc3c7;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 5px 10px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #3498db;
            color: #ffffff !important;
            transform: translateX(-5px);
        }
        
        .main-content {
            background-color: #2c3e50;
            min-height: calc(100vh - 76px);
            padding: 20px;
        }
        
        .card {
            background: linear-gradient(135deg, #34495e, #2c3e50);
            border: 1px solid #3498db;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
        }
        
        .card-header {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            font-weight: 600;
            border-radius: 12px 12px 0 0 !important;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            border: none;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            border: none;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
        }
        
        .table-dark {
            background-color: #34495e;
        }
        
        .table-dark th {
            background-color: #2c3e50;
            border-color: #3498db;
        }
        
        .status-online {
            color: #27ae60;
        }
        
        .status-offline {
            color: #e74c3c;
        }
        
        .camera-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .camera-card {
            position: relative;
            background: #34495e;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        
        .camera-stream {
            width: 100%;
            height: 200px;
            background: #2c3e50;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
        }
        
        .camera-info {
            padding: 15px;
            background: linear-gradient(135deg, #34495e, #2c3e50);
        }
        
        .loading-spinner {
            border: 3px solid #34495e;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-card {
            background: linear-gradient(135deg, #8e44ad, #9b59b6);
            color: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stats-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 76px;
                right: -250px;
                width: 250px;
                height: calc(100vh - 76px);
                z-index: 1000;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
            
            .camera-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- شريط التنقل العلوي -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-video me-2"></i>
                نظام المراقبة الذكي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-user me-1"></i>
                            مرحباً، {{ current_user.username if current_user.is_authenticated else 'ضيف' }}
                        </span>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            تسجيل الخروج
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- الشريط الجانبي -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <nav class="nav flex-column pt-3">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-2"></i>
                            الرئيسية
                        </a>
                        <a class="nav-link" href="{{ url_for('cameras') }}">
                            <i class="fas fa-video me-2"></i>
                            الكاميرات
                        </a>
                        <a class="nav-link" href="{{ url_for('recordings') }}">
                            <i class="fas fa-film me-2"></i>
                            التسجيلات
                        </a>
                        <a class="nav-link" href="{{ url_for('events') }}">
                            <i class="fas fa-bell me-2"></i>
                            الأحداث
                        </a>
                        {% if current_user.role in ['admin', 'user'] %}
                        <a class="nav-link" href="{{ url_for('camera_discovery_page') }}">
                            <i class="fas fa-search me-2"></i>
                            اكتشاف الكاميرات
                        </a>
                        {% endif %}
                        {% if current_user.role == 'admin' %}
                        <a class="nav-link" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات
                        </a>
                        {% endif %}
                    </nav>
                </div>
            </div>
            
            <!-- المحتوى الرئيسي -->
            <div class="col-md-10 p-0">
            {% else %}
            <div class="col-12 p-0">
            {% endif %}
                <div class="main-content">
                    <!-- رسائل التنبيه -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
